import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Lock, Edit3, Info } from "lucide-react";
import { useToastContext } from "@/contexts/ToastContext";

// Import our created components
import { OutputsStep } from "@/components/ConnectionForm/OutputsStep";
import { ByProductsStep } from "@/components/ConnectionForm/ByProductsStep";
import { InputsStep } from "@/components/ConnectionForm/InputsStep";
import { FinancialStep } from "@/components/ConnectionForm/FinancialStep";
import { StepNavigation } from "@/components/ConnectionForm/StepNavigation";
import { processConnectionsFromFormData } from "@/utils/connectionUtils";
import {
  ConnectionFormProps,
  OutputForm,
  FormData,
  STEPS,
  StepId,
  EnergyOutput,
  MaterialOutput,
  EnergyInput,
  EmissionInput,
  MaterialInput
} from "@/components/ConnectionForm/types";
import { useConnectionFormData } from "@/hooks/useConnectionFormData";
import { useSectors } from "@/hooks/useSectors";
import { getSectorUuidFromIndustryId } from "@/services/activitiesApi";
import { findActivityUuidByName } from "@/services/connectionFormApi";

export function ConnectionFormDialog({
  open,
  onClose,
  onComplete,
  autoFillInputs = [],
  sourceNode = null,
  targetNode = null,
  availableNodes = [],
  incomingConnectionData = null,
  existingFormData = null,
  sectorUuid,
  activityUuid,
  industryId,
  // New dual tab props
  isScenarioMode = false,
  scenarioTabMode = 'current',
  onScenarioTabChange,
  baseScenarioData,
  baseScenarioName = 'Base Scenario',
  currentScenarioName = 'Current Scenario',
  isNewNode = false,
}: ConnectionFormProps) {
  const { toast } = useToastContext();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Dual tab state management
  const [activeScenarioTab, setActiveScenarioTab] = useState<'base' | 'current'>(scenarioTabMode);

  // Update active tab when prop changes
  useEffect(() => {
    setActiveScenarioTab(scenarioTabMode);
  }, [scenarioTabMode]);

  // API data hooks
  const { sectors } = useSectors();
  const [resolvedSectorUuid, setResolvedSectorUuid] = useState<string | undefined>(sectorUuid);
  const [resolvedActivityUuid, setResolvedActivityUuid] = useState<string | undefined>(activityUuid);

  // Use API data hooks
  const apiData = useConnectionFormData(resolvedActivityUuid, resolvedSectorUuid);

  // Debug logging for API data
  useEffect(() => {
    console.log('ConnectionFormDialog: API data state changed');
    console.log('- resolvedActivityUuid:', resolvedActivityUuid);
    console.log('- resolvedSectorUuid:', resolvedSectorUuid);
    console.log('- technologies count:', apiData.technologies.technologies.length);
    console.log('- materials count:', apiData.materials.materials.length);
    console.log('- energies count:', apiData.energies.energies.length);
    console.log('- emissions count:', apiData.emissions.emissions.length);
    console.log('- isLoading:', apiData.isLoading);
  }, [resolvedActivityUuid, resolvedSectorUuid, apiData]);

  // State for managing multiple outputs
  const [outputs, setOutputs] = useState<OutputForm[]>([]);
  const [activeOutputTab, setActiveOutputTab] = useState("output-0");

  // Debug outputs state changes
  useEffect(() => {
    console.log(`=== OUTPUTS STATE CHANGED ===`);
    console.log(`New outputs:`, outputs);
    console.trace('Stack trace for outputs change:');
  }, [outputs]);

  // State for managing technologies across all tabs
  const [technologies, setTechnologies] = useState<string[]>(["Technology 1"]);
  const [activeTechnology, setActiveTechnology] = useState("Technology 1");

  // State for technology-specific form data
  const [technologyFormData, setTechnologyFormData] = useState<Record<string, FormData>>({});

  // State to track available technologies from outputs
  const [availableTechnologies, setAvailableTechnologies] = useState<string[]>([]);
  
  // Flag to track if form should be submitted (only after completing all steps)
  const [shouldCompleteForm, setShouldCompleteForm] = useState(false);

  // Flag to track if we've loaded existing data to prevent overwriting
  const [hasLoadedExistingData, setHasLoadedExistingData] = useState(false);

  // Callback functions for when new resources are added
  const handleMaterialAdded = (material: any) => {
    // Refresh materials data
    apiData.materials.reload();
  };

  const handleEnergyAdded = (energy: any) => {
    // Refresh energies data
    apiData.energies.reload();
  };

  const handleEmissionAdded = (emission: any) => {
    // Refresh emissions data
    apiData.emissions.reload();
  };

  // Check if technology is selected (required for proceeding to other steps)
  const isTechnologySelected = () => {
    const currentTechData = getCurrentTechnologyFormData();
    return currentTechData.technology && currentTechData.technology.trim() !== "";
  };

  // Get selected activity name from sourceNode for display in dialog header
  const selectedActivityName = (sourceNode as any)?.data?.label || "New Activity";

  // Helper function to get form data based on current scenario tab
  const getFormDataForCurrentTab = () => {
    if (isScenarioMode && activeScenarioTab === 'base') {
      return baseScenarioData || {};
    } else {
      return existingFormData || {};
    }
  };

  // Helper function to determine if current tab is read-only
  const isCurrentTabReadOnly = () => {
    return isScenarioMode && activeScenarioTab === 'base';
  };

  // Resolve sector UUID from industry ID if not provided
  useEffect(() => {
    const resolveSectorUuid = async () => {
      if (sectorUuid) {
        setResolvedSectorUuid(sectorUuid);
        return;
      }

      if (industryId && sectors.length > 0) {
        try {
          const uuid = await getSectorUuidFromIndustryId(industryId);
          setResolvedSectorUuid(uuid);
        } catch (error) {
          console.error('Error resolving sector UUID:', error);
          // Fallback to first sector if available
          if (sectors.length > 0) {
            setResolvedSectorUuid(sectors[0].uuid);
          }
        }
      }
    };

    resolveSectorUuid();
  }, [sectorUuid, industryId, sectors]);

  // Resolve activity UUID from activity name if not provided
  useEffect(() => {
    const resolveActivityUuid = async () => {
      if (activityUuid) {
        setResolvedActivityUuid(activityUuid);
        return;
      }

      // Try to get activity name from sourceNode
      const activityName = selectedActivityName;
      if (activityName && activityName !== "New Activity" && resolvedSectorUuid) {
        try {
          const uuid = await findActivityUuidByName(resolvedSectorUuid, activityName, { toast });
          if (uuid) {
            setResolvedActivityUuid(uuid);
            console.log(`Resolved activity UUID: ${uuid} for activity: ${activityName}`);
          } else {
            console.log(`No activity UUID found for activity: ${activityName}`);
          }
        } catch (error) {
          console.error('Error resolving activity UUID:', error);
        }
      }
    };

    resolveActivityUuid();
  }, [activityUuid, selectedActivityName, resolvedSectorUuid, toast]);

  // Form data state
  const [formData, setFormData] = useState<FormData>({
    // Basic info
    activity: "",
    technology: "",
    // Technology life span
    startYear: "2000",
    endYear: "2075",
    // Keep for compatibility
    customTechnology: "",
    customActivity: "",
    
    // Step 1: Inputs (now Step 3) - Arrays for multiple entries
    energyInputs: [],
    emissions: [],
    materialInputs: [],
    
    // Legacy single entries - kept for compatibility
    energyInput: {
      source: "",
      unit: "GJ",
      cost: "",
      sec: ""
    },
    emission: {
      source: "",
      ef: "",
      unit: "GJ"
    },
    matInput: {
      material: "",
      unit: "Tonnes",
      cost: "",
      smc: ""
    },
    
    // Step 3: By-products (now Step 2)
    byproductTechnology: "", // Field for by-product technology
    byproductEnergy: {
      byproduct: "",
      unit: "GJ",
      bppo: "",
      connect: "",
      replaced: ""
    },
    byproductMat: {
      byproduct: "",
      unit: "Tonnes",
      bppo: "",
      connect: "",
      replaced: "",
      techEmissionFactor: "",
      emissionFactor: "",
      emissionUnit: "Tonnes"
    },
    
    // Step 4: Financial (remains Step 4)
    financial: {
      capacity: "",
      capacityUnit: "Tonnes/day",
      capitalCost: "",
      operatingMaintenanceCost: ""
    },
    
    // NEW: Add financialEntries to store multiple financial entries
    financialEntries: {}
  });

  // UI helper states
  const [energyInputAutoFillLabel, setEnergyInputAutoFillLabel] = useState("");
  const [matInputAutoFillLabel, setMatInputAutoFillLabel] = useState("");
  const [technologyAutoFillLabel, setTechnologyAutoFillLabel] = useState(""); // State for technology auto-fill
  
  // Initialize with one default output tab when dialog opens
  useEffect(() => {
    if (open) {
      // Check if we have existing form data to load
      if (existingFormData) {

        // Load existing outputs and ensure they're synchronized with technologies
        if (existingFormData.outputs && existingFormData.outputs.length > 0) {
          // Get the first technology that will be set as active
          const firstTechnology = existingFormData.technologies?.[0] || "Technology 1";

          // Ensure outputs are properly associated with the active technology
          const synchronizedOutputs = existingFormData.outputs.map(output => ({
            ...output,
            outputTechnology: firstTechnology // Ensure it matches the active technology
          }));

          setOutputs(synchronizedOutputs);
          setActiveOutputTab(synchronizedOutputs[0].id);
        } else {
          // If no outputs but we have technologies and technology form data, create default outputs
          if (existingFormData.technologies && existingFormData.technologies.length > 0 && existingFormData.technologyFormData) {
            const firstTech = existingFormData.technologies[0];
            const techFormData = existingFormData.technologyFormData[firstTech];

            if (techFormData && (techFormData.materialOutputs || techFormData.energyOutputs)) {
              const createdOutput = {
                id: "output-0",
                targetNode: "",
                outputTechnology: firstTech,
                energyOutputs: techFormData.energyOutputs || [],
                matOutputs: techFormData.materialOutputs || []
              };

              setOutputs([createdOutput]);
              setActiveOutputTab("output-0");
            }
          }
        }

        // Load existing technologies
        if (existingFormData.technologies && existingFormData.technologies.length > 0) {
          setTechnologies(existingFormData.technologies);
          setActiveTechnology(existingFormData.technologies[0]);
        }

        // Load existing technology form data
        if (existingFormData.technologyFormData) {
          // Ensure each technology form data has the correct technology field set
          const correctedTechnologyFormData: Record<string, FormData> = {};
          Object.entries(existingFormData.technologyFormData).forEach(([techName, techData]) => {
            correctedTechnologyFormData[techName] = {
              ...techData,
              technology: techName // Ensure the technology field matches the key
            };
          });

          setTechnologyFormData(correctedTechnologyFormData);
        }

        // Load existing global form data
        if (existingFormData.formData) {
          setFormData(existingFormData.formData);
        } else if (existingFormData.technologies && existingFormData.technologies.length > 0) {
          // If no global formData but we have technologies, ensure the technology field is set
          setFormData(prev => ({
            ...prev,
            technology: existingFormData.technologies[0]
          }));
        }

        setHasLoadedExistingData(true);
        return; // Skip default initialization if we loaded existing data
      }

      // Default initialization for new connections (only if we haven't loaded existing data)
      if (hasLoadedExistingData) {
        return;
      }
      const initialEnergyOutput: EnergyOutput = {
        id: `energy-${Date.now()}-0`,
        energy: "",
        unit: "GJ",
        sec: "",
        final: false,
        connect: "",
        qty: "",
        qtyUnit: "GJ",
        destinationTechnology: "" // Initialize destination technology
      };

      const initialMatOutput: MaterialOutput = {
        id: `material-${Date.now()}-0`,
        material: "",
        unit: "Tonnes",
        smc: "",
        final: false,
        connect: "", // Ensure connect property is initialized
        qty: "",
        qtyUnit: "Tonnes",
        destinationTechnology: "" // Initialize destination technology
      };

      const initialOutput: OutputForm = {
        id: "output-0",
        targetNode: targetNode?.id || "",
        outputTechnology: "Technology 1",
        energyOutputs: [initialEnergyOutput],
        matOutputs: [initialMatOutput]
      };

      setOutputs([initialOutput]);
      setActiveOutputTab("output-0");
      setTechnologies(["Technology 1"]);
      setActiveTechnology("Technology 1");

      // Initialize technology form data with proper structure
      const initialTechFormData: FormData = {
        technology: "",
        activity: "",
        startYear: "2000",
        endYear: "2075",
        customTechnology: "",
        customActivity: "",
        // Arrays for multiple entries (actual form data)
        energyInputs: [],
        emissions: [],
        materialInputs: [],
        // Legacy single entries (for compatibility)
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        // By-products
        byproductTechnology: "Technology 1",
        byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
        byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
        energyByProducts: [],
        materialByProducts: [],
        // Financial
        financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCost: "", operatingMaintenanceCost: "" },
        financialEntries: {}
      };

      // Initialize with one empty entry for each array
      const initialEnergyInput: EnergyInput = {
        id: `energy-${Date.now()}-0`,
        source: "",
        unit: "GJ",
        cost: "",
        sec: "",
        sourceActivity: "Nil",
        technology: "Nil",
        lowerBound: "",
        upperBound: ""
      };

      const initialEmissionInput: EmissionInput = {
        id: `emission-${Date.now()}-0`,
        source: "",
        factor: "",
        unit: "kg"
      };

      const initialMaterialInput: MaterialInput = {
        id: `material-${Date.now()}-0`,
        material: "",
        unit: "Tonnes",
        cost: "",
        smc: "",
        sourceActivity: "Nil",
        technology: "Nil",
        lowerBound: "",
        upperBound: ""
      };

      // Update the technology form data with initial arrays
      initialTechFormData.energyInputs = [initialEnergyInput];
      initialTechFormData.emissions = [initialEmissionInput];
      initialTechFormData.materialInputs = [initialMaterialInput];

      setTechnologyFormData({
        "Technology 1": initialTechFormData
      });
    }
  }, [open, targetNode, existingFormData]);

  // Reset the form ONLY when dialog closes
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  // Track available technologies from inputs (since Inputs is now first)
  useEffect(() => {
    const uniqueTechnologies = new Set<string>();

    // Add technology from formData (from Inputs step)
    if (formData.technology) {
      uniqueTechnologies.add(formData.technology);
    }

    // Also add technologies from outputs for backward compatibility
    outputs.forEach(output => {
      if (output.outputTechnology) {
        uniqueTechnologies.add(output.outputTechnology);
      }
    });

    setAvailableTechnologies(Array.from(uniqueTechnologies));
  }, [formData.technology, outputs]);

  // Sync active technology changes with form data
  useEffect(() => {
    // Ensure we have form data for the active technology
    if (activeTechnology && !technologyFormData[activeTechnology]) {
      const initialFormData: FormData = {
        technology: activeTechnology,
        activity: "",
        startYear: "2000",
        endYear: "2075",
        customTechnology: "",
        customActivity: "",
        // Arrays for multiple entries (actual form data)
        energyInputs: [],
        emissions: [],
        materialInputs: [],
        // Legacy single entries (for compatibility)
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        // By-products
        byproductTechnology: activeTechnology,
        byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
        byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
        energyByProducts: [],
        materialByProducts: [],
        // Financial
        financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCost: "", operatingMaintenanceCost: "" },
        financialEntries: {}
      };

      setTechnologyFormData(prev => ({
        ...prev,
        [activeTechnology]: initialFormData
      }));
    }
  }, [activeTechnology, technologyFormData]);

  // Sync activeOutputTab with activeTechnology
  useEffect(() => {
    // Find the output that corresponds to the current technology
    const currentTechOutput = outputs.find(o => o.outputTechnology === activeTechnology);
    if (currentTechOutput && activeOutputTab !== currentTechOutput.id) {
      setActiveOutputTab(currentTechOutput.id);
    }
  }, [activeTechnology, outputs, activeOutputTab]);

  // Check and apply incoming connection data for Node B inputs
  useEffect(() => {
    if (!open || !incomingConnectionData) return;
    
    try {
      console.log("Applying incoming connection data:", incomingConnectionData);
      
      // Only apply the incoming connection data if we're on inputs or byproducts step
      if (currentStepIndex !== 0 && currentStepIndex !== 2) return;
      
      // Apply technology from incoming connection to technology field
      if (incomingConnectionData.outputTechnology) {
        setFormData(prev => ({
          ...prev,
          technology: incomingConnectionData.outputTechnology,
          byproductTechnology: incomingConnectionData.outputTechnology // Also set by-product technology
        }));
        
        setTechnologyAutoFillLabel(`${incomingConnectionData.outputTechnology} (auto-filled from connection)`);
      }
      
      // Apply energy output from incoming connection to energy input
      if (incomingConnectionData.energyOutput?.energy) {
        setFormData(prev => ({
          ...prev,
          energyInput: {
            ...prev.energyInput,
            source: incomingConnectionData.energyOutput.energy,
            unit: incomingConnectionData.energyOutput.unit || "GJ", 
            sec: incomingConnectionData.energyOutput.sec || ""
          }
        }));
        
        setEnergyInputAutoFillLabel(`${incomingConnectionData.energyOutput.energy} (auto-filled from connection)`);
      }
      
      // Apply material output from incoming connection to material input
      if (incomingConnectionData.matOutput?.material) {
        setFormData(prev => ({
          ...prev,
          matInput: {
            ...prev.matInput,
            material: incomingConnectionData.matOutput.material,
            unit: incomingConnectionData.matOutput.unit || "Tonnes",
            smc: incomingConnectionData.matOutput.smc || ""
          }
        }));
        
        setMatInputAutoFillLabel(`${incomingConnectionData.matOutput.material} (auto-filled from connection)`);
      }
    } catch (err) {
      console.error("Error applying incoming connection data:", err);
    }
  }, [open, incomingConnectionData, currentStepIndex]);

  // Auto-fill inputs based on source node outputs
  useEffect(() => {
    if (!open || !autoFillInputs || autoFillInputs.length === 0) return;
    
    try {
      // Only apply if we're on the inputs or byproducts step
      if (currentStepIndex !== 0 && currentStepIndex !== 2) return;
      
      // Process technology from source node
      const techItems = autoFillInputs
        .filter(item => item && item.technology)
        .map(item => ({
          label: `${item.technology} (auto-filled from ${item.nodeName})`,
          value: item.technology
        }));
      
      // Process energy outputs from source node
      const energyItems = autoFillInputs
        .filter(item => item && item.outputs && item.outputs.energy)
        .map(item => ({
          label: `${item.outputs!.energy} (auto-filled from ${item.nodeName})`,
          value: item.outputs!.energy,
          unit: item.outputs!.energyUnit || "GJ",
          sec: item.outputs!.energySEC || ""
        }));
        
      // Process material outputs from source node
      const materialItems = autoFillInputs
        .filter(item => item && item.outputs && item.outputs.material)
        .map(item => ({
          label: `${item.outputs!.material} (auto-filled from ${item.nodeName})`,
          value: item.outputs!.material,
          unit: item.outputs!.materialUnit || "Tonnes",
          smc: item.outputs!.materialSMC || ""
        }));
      
      // Apply technology auto-fill if available and not already set by incoming connection
      if (techItems.length > 0 && !incomingConnectionData?.outputTechnology) {
        setFormData(prev => ({
          ...prev,
          technology: techItems[0].value,
          byproductTechnology: techItems[0].value
        }));
        setTechnologyAutoFillLabel(techItems[0].label);
      }
      
      // Apply energy auto-fill if available and not already set by incoming connection
      if (energyItems.length > 0 && !incomingConnectionData?.energyOutput?.energy) {
        setFormData(prev => ({
          ...prev,
          energyInput: {
            ...prev.energyInput,
            source: energyItems[0].value,
            unit: energyItems[0].unit,
            sec: energyItems[0].sec
          }
        }));
        setEnergyInputAutoFillLabel(energyItems[0].label);
      }
      
      // Apply material auto-fill if available and not already set by incoming connection
      if (materialItems.length > 0 && !incomingConnectionData?.matOutput?.material) {
        setFormData(prev => ({
          ...prev,
          matInput: {
            ...prev.matInput,
            material: materialItems[0].value,
            unit: materialItems[0].unit,
            smc: materialItems[0].smc
          }
        }));
        setMatInputAutoFillLabel(materialItems[0].label);
      }
    } catch (err) {
      console.error("Error processing autofill data:", err);
    }
  }, [open, autoFillInputs, currentStepIndex, incomingConnectionData]);

  // Reset the form to initial state
  const resetForm = () => {
    setCurrentStepIndex(0);
    setErrors({});
    setShouldCompleteForm(false);
    setHasLoadedExistingData(false); // Reset the flag
    setFormData({
      activity: "",
      technology: "",
      startYear: "2000",
      endYear: "2075",
      technologyEmission: "",
      customTechnology: "",
      customActivity: "",
      byproductTechnology: "", // Reset byproduct technology
      energyInputs: [], // Initialize empty arrays for multiple entries
      emissions: [],
      materialInputs: [],
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "GJ" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "Tonnes" },
      financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCost: "", operatingMaintenanceCost: "" },
      financialEntries: {}
    });
    setEnergyInputAutoFillLabel("");
    setMatInputAutoFillLabel("");
    setTechnologyAutoFillLabel(""); // Reset technology auto-fill label
    
    // Reset outputs and technologies
    setOutputs([]);
    setActiveOutputTab("");
    setTechnologies(["Technology 1"]);
    setActiveTechnology("Technology 1");
    setAvailableTechnologies([]); // Reset available technologies

    // Reset technology-specific form data
    const resetTechFormData: FormData = {
      technology: "",
      activity: "",
      startYear: "2000",
      endYear: "2075",
      technologyEmission: "",
      customTechnology: "",
      customActivity: "",
      // Arrays for multiple entries (actual form data)
      energyInputs: [],
      emissions: [],
      materialInputs: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Technology 1",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCost: "", operatingMaintenanceCost: "" },
      financialEntries: {}
    };

    setTechnologyFormData({
      "Technology 1": resetTechFormData
    });
  };

  // Handle form input changes
  const updateFormField = (section: string, field: string, value: string) => {
    // Check if this is a nested path for financialEntries
    if (section.startsWith('financialEntries.')) {
      const [, key, nestedField] = section.split('.');
      
      setFormData(prev => ({
        ...prev,
        financialEntries: {
          ...prev.financialEntries,
          [key]: {
            ...(prev.financialEntries?.[key] || { capacity: '', capacityUnit: 'Tonnes/day', capitalCost: '', operatingMaintenanceCost: '' }),
            [nestedField || field]: value
          }
        }
      }));
    } else {
      // Regular section update
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }));
    }
    
    // Clear related validation errors if any
    if (errors[`${section}.${field}`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${section}.${field}`];
        return newErrors;
      });
    }
  };

  // Simple field update for top-level fields
  const updateField = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear validation error if any
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Add a new technology (replaces addNewOutput)
  const addNewTechnology = (customTechName?: string) => {
    const newTechName = customTechName || `Technology ${technologies.length + 1}`;

    // Only add if it doesn't already exist
    if (!technologies.includes(newTechName)) {
      setTechnologies([...technologies, newTechName]);
      setActiveTechnology(newTechName);

      // Initialize form data for the new technology
      const initialFormData: FormData = {
        technology: newTechName,
        activity: "",
        technologyEmission: "",
        customTechnology: "",
        customActivity: "",
        // Arrays for multiple entries (actual form data)
        energyInputs: [],
        emissions: [],
        materialInputs: [],
        // Legacy single entries (for compatibility)
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        // By-products
        byproductTechnology: newTechName,
        byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
        byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
        energyByProducts: [],
        materialByProducts: [],
        // Financial
        financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCost: "", operatingMaintenanceCost: "" },
        financialEntries: {}
      };

      setTechnologyFormData(prev => ({
        ...prev,
        [newTechName]: initialFormData
      }));

      // Also create a corresponding output for this technology
      const newOutputId = `output-${outputs.length}`;
      const initialEnergyOutput: EnergyOutput = {
        id: `energy-${Date.now()}-0`,
        energy: "",
        unit: "GJ",
        sec: "",
        final: false,
        connect: "",
        qty: "",
        qtyUnit: "GJ"
      };

      const initialMatOutput: MaterialOutput = {
        id: `material-${Date.now()}-0`,
        material: "",
        unit: "Tonnes",
        smc: "",
        final: false,
        connect: "",
        qty: "",
        qtyUnit: "Tonnes"
      };

      const newOutput: OutputForm = {
        id: newOutputId,
        targetNode: "",
        outputTechnology: newTechName,
        energyOutputs: [initialEnergyOutput],
        matOutputs: [initialMatOutput]
      };

      setOutputs([...outputs, newOutput]);
      setActiveOutputTab(newOutputId);
    } else {
      // If technology already exists, just switch to it
      setActiveTechnology(newTechName);
    }
  };

  // Get form data for current technology
  const getCurrentTechnologyFormData = (): FormData => {
    if (technologyFormData[activeTechnology]) {
      return technologyFormData[activeTechnology];
    }
    // Fallback: create initial form data for the technology
    const fallbackFormData: FormData = {
      technology: activeTechnology,
      activity: "",
      startYear: "2000",
      endYear: "2075",
      technologyEmission: "",
      customTechnology: "",
      customActivity: "",
      // Arrays for multiple entries (actual form data)
      energyInputs: [],
      emissions: [],
      materialInputs: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: activeTechnology,
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCost: "", operatingMaintenanceCost: "" },
      financialEntries: {}
    };

    return fallbackFormData;
  };

  // Update form data for current technology
  const updateTechnologyFormData = (field: string, value: string | any[]) => {
    setTechnologyFormData(prev => {
      // Ensure we have form data for the current technology
      const currentTechData = prev[activeTechnology] || getCurrentTechnologyFormData();

      return {
        ...prev,
        [activeTechnology]: {
          ...currentTechData,
          [field]: value
        }
      };
    });

    // Also update global formData for backward compatibility
    if (typeof value === 'string') {
      updateField(field, value);
    }
  };

  // Update form field for current technology (for nested objects)
  const updateTechnologyFormField = (section: string, field: string, value: string) => {
    setTechnologyFormData(prev => {
      // Ensure we have form data for the current technology
      const currentTechData = prev[activeTechnology] || getCurrentTechnologyFormData();

      // Check if this is a nested path for financialEntries
      if (section.startsWith('financialEntries.')) {
        const [, key, nestedField] = section.split('.');

        // Get existing financial data to preserve other fields
        const existingFinancialData = currentTechData.financialEntries?.[key] ||
          currentTechData.financial ||
          { capacity: '', capacityUnit: 'Tonnes/day', capitalCost: '', operatingMaintenanceCost: '' };

        const updatedFinancialData = {
          ...existingFinancialData,
          [nestedField || field]: value
        };

        return {
          ...prev,
          [activeTechnology]: {
            ...currentTechData,
            financialEntries: {
              ...currentTechData.financialEntries,
              [key]: updatedFinancialData
            },
            // Also update main financial object for consistency
            financial: {
              ...currentTechData.financial,
              [nestedField || field]: value
            }
          }
        };
      } else {
        // Regular section update
        return {
          ...prev,
          [activeTechnology]: {
            ...currentTechData,
            [section]: {
              ...currentTechData[section],
              [field]: value
            }
          }
        };
      }
    });

    // Also update global formData for backward compatibility
    updateFormField(section, field, value);
  };

  // Update technology name when technology is selected in dropdown
  const updateTechnologyName = (oldTechName: string, newTechName: string) => {
    if (oldTechName !== newTechName) {
      setTechnologies(prev => prev.map(tech => tech === oldTechName ? newTechName : tech));
      setActiveTechnology(newTechName);

      // Move form data from old technology name to new technology name
      setTechnologyFormData(prev => {
        const newData = { ...prev };
        if (newData[oldTechName]) {
          newData[newTechName] = { ...newData[oldTechName] };
          delete newData[oldTechName];
        }
        return newData;
      });

      // Also update the corresponding output
      setOutputs(prev => prev.map(output =>
        output.outputTechnology === oldTechName
          ? { ...output, outputTechnology: newTechName }
          : output
      ));
    }
  };

  // Handle output field updates including technology synchronization
  const updateOutputField = (outputId: string, fieldPath: string, value: any) => {
    console.log(`=== REAL updateOutputField CALLED ===`);
    console.log(`outputId:`, outputId);
    console.log(`fieldPath:`, fieldPath);
    console.log(`value:`, value);

    setOutputs(prevOutputs => {
      console.log(`Previous outputs:`, prevOutputs);
      const newOutputs = [...prevOutputs];
      const outputIndex = newOutputs.findIndex(output => output.id === outputId);
      console.log(`Found output index:`, outputIndex);

      if (outputIndex !== -1) {
        // Handle nested paths like 'energyOutput.energy'
        if (fieldPath.includes('.')) {
          const [section, field] = fieldPath.split('.');
          newOutputs[outputIndex] = {
            ...newOutputs[outputIndex],
            [section]: {
              ...newOutputs[outputIndex][section],
              [field]: value
            }
          };
        } else {
          // Handle direct properties like 'outputTechnology', 'energyOutputs', 'matOutputs'
          newOutputs[outputIndex] = {
            ...newOutputs[outputIndex],
            [fieldPath]: value
          };

          // Synchronize technology selection with byproduct immediately
          if (fieldPath === 'outputTechnology' && value) {
            setFormData(prev => ({
              ...prev,
              byproductTechnology: value
            }));
            setTechnologyAutoFillLabel(`${value} (synchronized from output)`);
          }
        }
      }

      return newOutputs;
    });

    // Clear related validation errors if any
    const errorKey = `${outputId}.${fieldPath}`;
    if (errors[errorKey]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }
  };

  // Function to clear specific validation errors
  const clearError = (errorKey: string) => {
    console.log(`Clearing error: ${errorKey}`);
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[errorKey];
      return newErrors;
    });
  };

  // Validate the current step
  const validateStep = () => {
    const newErrors: Record<string, string> = {};

    console.log(`Validating step ${currentStepIndex} (${STEPS[currentStepIndex].id})`);

    // Validation logic for each step (only enforce strict validation on outputs step)
    switch (currentStepIndex) {
      case 1: { // Outputs
        if (outputs.length === 0) {
          console.log("No outputs defined");
          newErrors["outputs"] = "At least one output must be defined";
          break;
        }

        // Check active output only for validation
        const output = outputs.find(o => o.id === activeOutputTab);
        if (output) {
          console.log("Validating output:", output);
          
          // Validate output technology selection
          if (!output.outputTechnology) {
            console.log("Missing output technology");
            newErrors[`${output.id}.outputTechnology`] = "Please select a technology";
          }
          
          // Validate that there's at least one valid energy or material output
          const hasValidEnergyOutput = output.energyOutputs.some(e => !!e.energy);
          const hasValidMaterialOutput = output.matOutputs.some(m => !!m.material);
          
          if (!hasValidEnergyOutput && !hasValidMaterialOutput) {
            console.log("Missing both energy and material outputs");
            // Add error to the first energy and material output
            if (output.energyOutputs.length > 0) {
              newErrors[`${output.id}.energyOutputs.${output.energyOutputs[0].id}.energy`] = 
                "At least one output type is required";
            }
            if (output.matOutputs.length > 0) {
              newErrors[`${output.id}.matOutputs.${output.matOutputs[0].id}.material`] = 
                "At least one output type is required";
            }
          }
          
          // Note: Removed destination validation for outputs
          // Users can mark outputs as final outputs or connect them to destinations as needed
          // The system will handle final output creation automatically
        }
        break;
      }
        
      // Other steps don't have strict validation
      case 0: // Inputs
        console.log("Input step - no required validation");
        break;

      case 2: // By-products
        console.log("By-products step - no required validation");
        break;

      case 3: // Financial
        console.log("Financial step - no required validation");
        break;
    }
    
    console.log("Validation errors:", Object.keys(newErrors).length > 0 ? newErrors : "None");
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Navigation: Next step
  const handleNext = () => {
    console.log(`Attempting to move from step ${currentStepIndex} (${STEPS[currentStepIndex].label}) to next step`);

    // Check if technology is selected when moving from inputs step (step 0)
    if (currentStepIndex === 0) {
      if (!isTechnologySelected()) {
        toast({
          title: "Technology Required",
          description: "Please select a technology before proceeding to the next step.",
          variant: "destructive"
        });
        return;
      }
    }

    // Validate strictly only for outputs step (step 1)
    if (currentStepIndex === 1) {
      // Run validation
      const isValid = validateStep();

      if (!isValid) {
        console.log("Output step validation failed, staying on current step");
        return;
      }
    }
    
    // Check if we're on the last step (Financial)
    if (currentStepIndex === STEPS.length - 1) {
      console.log("On last step, will submit form");
      setShouldCompleteForm(true);
      handleFinalSubmit();
    } else {
      // Move to the next step
      setCurrentStepIndex(prevIndex => {
        const nextIndex = prevIndex + 1;
        console.log(`Moving to step ${nextIndex} (${STEPS[nextIndex].label})`);
        return nextIndex;
      });
    }
  };

  // Navigation: Previous step
  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prevIndex => {
        const nextIndex = prevIndex - 1;
        console.log(`Moving back to step ${nextIndex} (${STEPS[nextIndex].label})`);
        return nextIndex;
      });
    }
  };

  // New function for final submission after completing all steps
  const handleFinalSubmit = () => {
    console.log("Performing final form submission");
    
    // Validate outputs step again to ensure it's still valid
    const currentTab = currentStepIndex;
    setCurrentStepIndex(1);
    const isOutputsValid = validateStep();
    setCurrentStepIndex(currentTab);

    if (!isOutputsValid) {
      toast({
        title: "Validation Error",
        description: "Please complete the output section before submitting." ,
        variant: "destructive"
      });
      setCurrentStepIndex(1); // Go back to outputs step if it's invalid
      setShouldCompleteForm(false);
      return;
    }
    
    try {
      // Build success toast message
      const sourceLabel = sourceNode?.data?.label || "Node";
      
      // Get all connections from form data
      const connections = processConnectionsFromFormData(
        { ...formData, outputs },
        sourceNode?.id || "",
        [] // We don't check for existing edges here since that's done in IndustryFlow
      );
      
      // Count total valid connections
      const validConnectionCount = connections.length;
      
      // FIXED: Don't merge technology-specific data - preserve it separately
      // Keep technology-specific data intact for proper multi-technology support
      const finalFormData = {
        ...formData, // Global form data (for backward compatibility)
        outputs, // Add all outputs to the form data
        technologyFormData, // Add all technology-specific form data (KEEP SEPARATE!)
        technologies, // Add the list of technologies
        activeTechnology, // Add the currently active technology
        formCompleted: true, // Add flag to indicate form was fully completed
        connectionCount: validConnectionCount, // Add connection count for the parent component
        processedConnections: connections // Include processed connections for parent
      };
      
      // Use manual entry values if provided
      if (formData.customTechnology && formData.customActivity) {
        finalFormData.technology = formData.customTechnology;
        finalFormData.activity = formData.customActivity;
      }
      
      const connectionMessage = validConnectionCount > 0
        ? `${validConnectionCount} connection${validConnectionCount !== 1 ? 's' : ''} created from ${sourceLabel}`
        : `Activity created: ${sourceLabel} (no connections)`;
      
      toast({ 
        title: "Connection completed", 
        description: connectionMessage
      });
      
      console.log("Submitting final form data with connections:", {
        ...finalFormData,
        technologyFormDataSummary: Object.keys(technologyFormData).map(tech => ({
          technology: tech,
          hasInputs: technologyFormData[tech]?.energyInputs?.length > 0 || technologyFormData[tech]?.materialInputs?.length > 0,
          hasEmissions: technologyFormData[tech]?.emissions?.length > 0,
          hasFinancial: !!technologyFormData[tech]?.financial?.capacity
        }))
      });
      
      // Pass form data to parent component
      onComplete(finalFormData);
      
      // Reset form state
      resetForm();
    } catch (error) {
      console.error("Error submitting connection form:", error);
      toast({ 
        title: "Connection error", 
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
      setShouldCompleteForm(false);
    }
  };

  // Regular form submission handler (converted to be only for DOM events)
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Only handle form submission here for the button click events
    if (currentStepIndex === STEPS.length - 1) {
      // Only trigger submission on final step
      setShouldCompleteForm(true);
      handleFinalSubmit();
    } else {
      // Otherwise just navigate to next step
      handleNext();
    }
  };

  // Render content based on current step
  const renderStepContent = (stepId: StepId, readOnly: boolean = false) => {
    console.log(`Rendering content for step: ${currentStepIndex} (${stepId}), readOnly: ${readOnly}`);
    
    switch (stepId) {
      case "outputs":
        return (
          <OutputsStep
            outputs={outputs}
            setOutputs={setOutputs}
            activeOutputTab={activeOutputTab}
            setActiveOutputTab={setActiveOutputTab}
            errors={errors}
            availableNodes={availableNodes}
            updateOutputField={(fieldPath, value) => {
              console.log(`=== WRAPPER updateOutputField CALLED ===`);
              console.log(`fieldPath:`, fieldPath);
              console.log(`value:`, value);
              console.log(`activeTechnology:`, activeTechnology);
              console.log(`outputs:`, outputs);

              // Find the output that corresponds to the current technology
              const currentTechOutput = outputs.find(o => o.outputTechnology === activeTechnology);
              console.log(`currentTechOutput:`, currentTechOutput);

              if (currentTechOutput) {
                console.log(`Calling real updateOutputField with:`, currentTechOutput.id, fieldPath, value);
                updateOutputField(currentTechOutput.id, fieldPath, value);
              } else {
                console.log(`No current tech output found for technology:`, activeTechnology);
              }
            }}
            clearError={clearError}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            onAddTechnology={addNewTechnology}
            apiTechnologies={apiData.technologies.technologies}
            apiMaterials={apiData.materials.materials}
            apiEnergies={apiData.energies.energies}
            apiEmissions={apiData.emissions.emissions}
            isLoadingApiData={apiData.isLoading}
            readOnly={readOnly}
            sectorUuid={resolvedSectorUuid}
            onMaterialAdded={handleMaterialAdded}
            onEnergyAdded={handleEnergyAdded}
          />
        );

      case "byproducts":
        return (
          <ByProductsStep
            formData={getCurrentTechnologyFormData()}
            updateFormField={updateTechnologyFormField}
            errors={errors}
            availableNodes={availableNodes}
            technologyAutoFillLabel={technologyAutoFillLabel}
            availableTechnologies={availableTechnologies}
            updateField={updateTechnologyFormData}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            onAddTechnology={addNewTechnology}
            apiTechnologies={apiData.technologies.technologies}
            apiMaterials={apiData.materials.materials}
            apiEnergies={apiData.energies.energies}
            apiEmissions={apiData.emissions.emissions}
            isLoadingApiData={apiData.isLoading}
            readOnly={readOnly}
            sectorUuid={resolvedSectorUuid}
            onMaterialAdded={handleMaterialAdded}
            onEnergyAdded={handleEnergyAdded}
          />
        );

      case "inputs":
        return (
          <InputsStep
            formData={getCurrentTechnologyFormData()}
            updateField={updateTechnologyFormData}
            updateFormField={updateTechnologyFormField}
            errors={errors}
            usingManualEntry={false}
            energyInputAutoFillLabel={energyInputAutoFillLabel}
            matInputAutoFillLabel={matInputAutoFillLabel}
            technologyAutoFillLabel={technologyAutoFillLabel}
            availableTechnologies={availableTechnologies}
            availableNodes={availableNodes}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            onAddTechnology={addNewTechnology}
            updateTechnologyName={updateTechnologyName}
            apiTechnologies={apiData.technologies.technologies}
            apiMaterials={apiData.materials.materials}
            apiEnergies={apiData.energies.energies}
            apiEmissions={apiData.emissions.emissions}
            isLoadingApiData={apiData.isLoading}
            onCreateTechnology={apiData.technologies.addTechnology}
            readOnly={readOnly}
            sectorUuid={resolvedSectorUuid}
            onMaterialAdded={handleMaterialAdded}
            onEnergyAdded={handleEnergyAdded}
            onEmissionAdded={handleEmissionAdded}
          />
        );

      case "financial":
        return (
          <FinancialStep
            formData={getCurrentTechnologyFormData()}
            updateFormField={updateTechnologyFormField}
            errors={errors}
            outputs={outputs}
            availableNodes={availableNodes}
            technologies={technologies}
            readOnly={readOnly}
          />
        );

      default:
        return <p>Unknown step</p>;
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        // Only close if isOpen is false AND we're not in the middle of active form use
        if (!isOpen) {
          // Check if this is a legitimate user closing action or an accidental one
          // Only close if we're not in the middle of filling out a multi-step form
          // or if we've explicitly completed the form
          if (shouldCompleteForm) {
            // If we've explicitly completed the form, it's safe to close
            onClose();
          } else {
            // If we're on any step other than the first and the form wasn't completed,
            // show a confirmation dialog before closing
            if (currentStepIndex > 0) {
              if (confirm("Are you sure you want to close? Your changes will be lost.")) {
                onClose();
              }
            } else {
              // If we're on the first step, it's safe to close without confirmation
              onClose();
            }
          }
        }
      }}
    >
      <DialogContent className="sm:max-w-[800px] h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Connection Details: {selectedActivityName}</DialogTitle>
          <DialogDescription>
            Capture output details and destination activities.
          </DialogDescription>
        </DialogHeader>

        {isScenarioMode ? (
          // Dual tab layout for scenario mode
          <Tabs
            value={activeScenarioTab}
            onValueChange={(value) => {
              const newTab = value as 'base' | 'current';
              setActiveScenarioTab(newTab);
              onScenarioTabChange?.(newTab);
            }}
            className="flex-1 flex flex-col overflow-hidden min-h-0"
          >
            <TabsList className="grid w-full grid-cols-2 mb-4 flex-shrink-0">
              <TabsTrigger value="base" className="flex items-center gap-2">
                <Lock size={16} />
                <div className="flex flex-col items-start">
                  <span className="font-medium">{baseScenarioName}</span>
                  <span className="text-xs text-muted-foreground">Base Scenario</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="current" className="flex items-center gap-2">
                <Edit3 size={16} />
                <div className="flex flex-col items-start">
                  <span className="font-medium">{currentScenarioName}</span>
                  <span className="text-xs text-muted-foreground">Current Scenario</span>
                </div>
              </TabsTrigger>
            </TabsList>

            {/* Base Scenario Tab - Read-only */}
            <TabsContent value="base" className="flex-1 overflow-hidden min-h-0">
              {isNewNode ? (
                <Alert className="m-4">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    This is a new node that doesn't exist in the base scenario.
                    No base scenario data is available for comparison.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="h-full flex flex-col overflow-hidden">
                  <Alert className="mx-4 mb-4 flex-shrink-0">
                    <Lock className="h-4 w-4" />
                    <AlertDescription>
                      This is the read-only data from the base scenario. Use the "Current Scenario" tab to make changes.
                    </AlertDescription>
                  </Alert>

                  <div className="flex-1 overflow-hidden px-4 pb-4">
                    <div className="h-full flex flex-col">
                      {/* Step indicators */}
                      <div className="flex-shrink-0 mb-4">
                        <StepNavigation steps={STEPS} currentStepIndex={currentStepIndex} />
                      </div>

                      {/* Current step content - read-only */}
                      <div className="flex-1 overflow-y-auto space-y-4">
                        {renderStepContent(STEPS[currentStepIndex].id, true)} {/* Pass readOnly=true */}
                      </div>

                      {/* Navigation buttons for base scenario (read-only) */}
                      <div className="flex justify-between pt-4 border-t flex-shrink-0 mt-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleBack}
                          disabled={currentStepIndex === 0}
                        >
                          Back
                        </Button>

                        {currentStepIndex < STEPS.length - 1 ? (
                          <Button
                            type="button"
                            onClick={handleNext}
                          >
                            Next
                          </Button>
                        ) : (
                          <Button type="button" disabled>
                            View Only
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Current Scenario Tab - Editable */}
            <TabsContent value="current" className="flex-1 overflow-hidden min-h-0">
              <div className="h-full flex flex-col overflow-hidden">
                <Alert className="mx-4 mb-4 flex-shrink-0">
                  <Edit3 className="h-4 w-4" />
                  <AlertDescription>
                    Make your changes here. This data will be saved to the current scenario.
                    {!isNewNode && baseScenarioData && " The form is pre-populated with base scenario data."}
                  </AlertDescription>
                </Alert>

                <div className="flex-1 overflow-hidden px-4 pb-4">
                  <form onSubmit={handleSubmit} className="h-full flex flex-col">
                    {/* Step indicators */}
                    <div className="flex-shrink-0 mb-4">
                      <StepNavigation steps={STEPS} currentStepIndex={currentStepIndex} />
                    </div>

                    {/* Current step content - editable */}
                    <div className="flex-1 overflow-y-auto space-y-4">
                      {renderStepContent(STEPS[currentStepIndex].id, false)} {/* Pass readOnly=false */}
                    </div>

                    {/* Navigation buttons */}
                    <div className="flex justify-between pt-4 border-t flex-shrink-0 mt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleBack}
                        disabled={currentStepIndex === 0}
                      >
                        Back
                      </Button>

                      {currentStepIndex < STEPS.length - 1 ? (
                        <Button
                          type="button"
                          onClick={handleNext}
                        >
                          Next
                        </Button>
                      ) : (
                        <Button type="button" onClick={handleFinalSubmit}>
                          Complete
                          </Button>
                        )}
                      </div>
                    </form>
                  </div>
                </div>
            </TabsContent>
          </Tabs>
        ) : (
          // Regular single form layout for inventory mode
          <div className="flex-1 flex flex-col min-h-0">
            <form onSubmit={handleSubmit} className="h-full flex flex-col">
              {/* Step indicators */}
              <div className="flex-shrink-0 mb-4">
                <StepNavigation steps={STEPS} currentStepIndex={currentStepIndex} />
              </div>

              {/* Current step content */}
              <div className="flex-1 overflow-y-auto">
                {renderStepContent(STEPS[currentStepIndex].id)}
              </div>

              {/* Error display area */}
              {Object.keys(errors).length > 0 && (
                <div className="px-3 py-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md flex-shrink-0 mt-4">
                  <p className="font-semibold mb-1">Please correct the following:</p>
                  <ul className="list-disc pl-5">
                    {Object.values(errors).slice(0, 3).map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                    {Object.values(errors).length > 3 && (
                      <li>...and {Object.values(errors).length - 3} more issues</li>
                    )}
                  </ul>
                </div>
              )}

              {/* Navigation buttons */}
              <div className="flex justify-between pt-4 border-t flex-shrink-0 mt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  disabled={currentStepIndex === 0}
                >
                  Back
                </Button>

                {currentStepIndex < STEPS.length - 1 ? (
                  <Button
                    type="button"  // Important: type="button" prevents form submission
                    onClick={handleNext}
                  >
                    Next
                  </Button>
                ) : (
                  <Button type="button" onClick={handleFinalSubmit}>
                    Complete
                  </Button>
                )}
              </div>
            </form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
