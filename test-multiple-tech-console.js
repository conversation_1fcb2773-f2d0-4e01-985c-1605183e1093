// Test script to verify multiple technology functionality
// Run this in the browser console after opening a technology dialog

// Test 1: Check if multiple technologies can be created
function testMultipleTechnologyCreation() {
  console.log("=== Testing Multiple Technology Creation ===");
  
  // This would be run when user clicks "Add Technology" button multiple times
  // The test verifies that each technology gets its own form data
  
  const mockTechnologyFormData = {
    "Technology 1": {
      technology: "Technology 1",
      energyInputs: [
        { id: "energy-1", source: "Electricity", unit: "GJ", cost: "100", sec: "0.5" }
      ],
      materialInputs: [
        { id: "material-1", material: "Steel", unit: "Tonnes", cost: "500", smc: "2.1" }
      ],
      emissions: [
        { id: "emission-1", source: "CO2", factor: "0.5", unit: "kg" }
      ]
    },
    "Technology 2": {
      technology: "Technology 2",
      energyInputs: [
        { id: "energy-2", source: "Natural Gas", unit: "GJ", cost: "80", sec: "0.3" }
      ],
      materialInputs: [
        { id: "material-2", material: "Aluminum", unit: "Tonnes", cost: "800", smc: "1.8" }
      ],
      emissions: [
        { id: "emission-2", source: "CH4", factor: "0.2", unit: "kg" }
      ]
    }
  };
  
  console.log("Mock technology form data:", mockTechnologyFormData);
  
  // Test that each technology has separate data
  const tech1Data = mockTechnologyFormData["Technology 1"];
  const tech2Data = mockTechnologyFormData["Technology 2"];
  
  console.log("Technology 1 has energy inputs:", tech1Data.energyInputs.length > 0);
  console.log("Technology 2 has energy inputs:", tech2Data.energyInputs.length > 0);
  console.log("Technologies have different energy sources:", 
    tech1Data.energyInputs[0].source !== tech2Data.energyInputs[0].source);
  
  return mockTechnologyFormData;
}

// Test 2: Check if save data structure preserves technology separation
function testSaveDataStructure(technologyFormData) {
  console.log("=== Testing Save Data Structure ===");
  
  // Simulate the save structure
  const finalFormData = {
    // Keep individual technology data intact
    technologyFormData,
    technologies: Object.keys(technologyFormData),
    activeTechnology: "Technology 1",
    // For backward compatibility, also include merged arrays
    energyInputs: Object.values(technologyFormData).flatMap(techData => 
      techData.energyInputs || []
    ),
    materialInputs: Object.values(technologyFormData).flatMap(techData => 
      techData.materialInputs || []
    ),
    emissions: Object.values(technologyFormData).flatMap(techData => 
      techData.emissions || []
    )
  };
  
  console.log("Final form data structure:", finalFormData);
  
  // Verify structure
  console.log("Has technologyFormData:", !!finalFormData.technologyFormData);
  console.log("Has technologies array:", Array.isArray(finalFormData.technologies));
  console.log("Technologies count:", finalFormData.technologies.length);
  console.log("Merged energyInputs count:", finalFormData.energyInputs.length);
  console.log("Merged materialInputs count:", finalFormData.materialInputs.length);
  console.log("Merged emissions count:", finalFormData.emissions.length);
  
  return finalFormData;
}

// Test 3: Check if load data structure can reconstruct technologies
function testLoadDataStructure(savedData) {
  console.log("=== Testing Load Data Structure ===");
  
  // Simulate loading the data back
  const loadedTechnologies = savedData.technologies || [];
  const loadedTechnologyFormData = savedData.technologyFormData || {};
  
  console.log("Loaded technologies:", loadedTechnologies);
  console.log("Loaded technology form data keys:", Object.keys(loadedTechnologyFormData));
  
  // Verify each technology can be reconstructed
  loadedTechnologies.forEach(techName => {
    const techData = loadedTechnologyFormData[techName];
    if (techData) {
      console.log(`Technology "${techName}" data:`, {
        hasEnergyInputs: techData.energyInputs?.length > 0,
        hasMaterialInputs: techData.materialInputs?.length > 0,
        hasEmissions: techData.emissions?.length > 0
      });
    } else {
      console.error(`Technology "${techName}" data missing!`);
    }
  });
  
  return { loadedTechnologies, loadedTechnologyFormData };
}

// Run all tests
function runAllTests() {
  console.log("🚀 Starting Multiple Technology Tests");
  
  const mockData = testMultipleTechnologyCreation();
  const savedData = testSaveDataStructure(mockData);
  const loadedData = testLoadDataStructure(savedData);
  
  console.log("✅ All tests completed");
  
  return {
    mockData,
    savedData,
    loadedData
  };
}

// Export for use
window.testMultipleTechnologies = runAllTests;

console.log("Multiple Technology Test Script Loaded");
console.log("Run: testMultipleTechnologies() to execute tests");
