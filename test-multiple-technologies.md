# Multiple Technology Save & Rendering Test

## Issue Description
When users add multiple technologies with values, the data doesn't save properly and hence doesn't render correctly when loaded back. When there are more than 1 technologies, they should be added as separate objects in technologies list, but instead they were being added to the same technology object. So when rendering back, there is only one technology visible.

## Root Cause Analysis
The issue was in the data structure handling during save and load operations:

1. **Save Issue**: In the `handleFinalSubmit` functions, the code was merging all technology inputs into single arrays (`allEnergyInputs`, `allMaterialInputs`, `allEmissions`) instead of preserving the technology-specific structure.

2. **Load Issue**: When switching to a new technology, the form data was being initialized with `existingFormData?.details` which is the old data structure, not the new technology-specific structure.

## Fix Applied

### 1. Save Logic Fix
Modified both `SupplierTechDialog.tsx` and `ConnectionFormDialog.tsx` to preserve the technology-specific structure:

```typescript
const finalFormData = {
  ...formData,
  outputs,
  // Keep individual technology data intact
  technologyFormData,
  technologies,
  activeTechnology,
  // For backward compatibility, also include merged arrays
  energyInputs: Object.values(technologyFormData).flatMap(techData => 
    (techData as FormData)?.energyInputs || []
  ),
  materialInputs: Object.values(technologyFormData).flatMap(techData => 
    (techData as FormData)?.materialInputs || []
  ),
  emissions: Object.values(technologyFormData).flatMap(techData => 
    (techData as FormData)?.emissions || []
  )
};
```

### 2. Technology Switching Fix
Fixed the useEffect that syncs active technology changes to create clean initial form data for new technologies instead of using old data structure:

```typescript
// Before (WRONG):
energyInputs: (existingFormData?.details?.inputs?.energies || []).map(...)

// After (CORRECT):
energyInputs: [], // Start empty for new technology
```

## Test Steps
1. Open Industry Flow page
2. Create a new connection between two nodes
3. Add first technology with some energy inputs, material inputs, and emissions
4. Add second technology with different inputs
5. Save the form
6. Reopen the form to edit
7. Verify both technologies are visible and have their respective data

## Expected Result
- Both technologies should be visible in the technology tabs
- Each technology should have its own separate form data
- Switching between technologies should show the correct data for each
- Saving and loading should preserve all technology data

## Debug Console Logs Added
- `SupplierTechDialog: Adding new technology:` - Shows when new technology is added
- `SupplierTechDialog: Creating initial form data for new technology:` - Shows when form data is initialized
- `SupplierTechDialog: Updated technology form data:` - Shows when technology form data is updated
- `SupplierTechDialog: Saving multiple technologies:` - Shows the data structure being saved
- `SupplierTechDialog: Loading existing technologies:` - Shows technologies being loaded
- `SupplierTechDialog: Loading existing technology form data:` - Shows technology form data being loaded
